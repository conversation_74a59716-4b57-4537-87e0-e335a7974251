<?php

declare(strict_types=1);

namespace app\back\modules\back\remarketingSources;

use app\back\components\AllowedLists;
use app\back\components\FormGrid;
use app\back\components\SessionMessages;
use app\back\components\validators\AllowedSiteValidator;
use app\back\components\validators\StringValidator;
use app\back\entities\Site;
use app\back\repositories\Sites;
use Yiisoft\Db\Connection\ConnectionInterface;

class RemarketingSourcesAddForm
{
    use FormGrid;

    #[AllowedSiteValidator]
    public int $siteId;
    #[StringValidator(1, 100)]
    public string $sourceCode;

    public function __construct(
        private readonly ConnectionInterface $db,
        private readonly Sites $sites,
        public readonly AllowedLists $allowedLists,
        private readonly SessionMessages $messages
    ) {
    }

    protected function blocks(): array
    {
        return [
            [
                $this->selectSiteCell(5, 'siteId', 'Site', [
                    'multiple' => false,
                ]),
                $this->textInputCell(5, 'sourceCode', 'Refcode prefix'),
                $this->submitCell(2, 'Add')
            ],
        ];
    }

    public function add(): void
    {
        /** @var Site $site */
        $site = $this->sites->findOneOr404(['site_id' => $this->siteId]);
        $sources = explode(',', $site->source_codes ?? '');
        if (in_array($this->sourceCode, $sources, true)) {
            $this->messages->error("Prefix <u>{$this->sourceCode}</u> for <u>{$site->site_name}</u> is already exist");
            return;
        }

        $sources[] = $this->sourceCode;
        $site->source_codes = implode(',', $sources);
        $this->sites->update($site, ['source_codes']);
    }
}
