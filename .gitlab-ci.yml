stages:
  - build
  - deploy

variables:
  PHP_VERSION:
    value: "8.3"
  ENVIRONMENT:
    value: "dev"
    options:
      - "dev"
      - "prod"
    description: "Environment"
  PRODUCTION_DEPLOY_NODE:
    value: "node-04.anal.tools node-05.anal.tools"
    options:
      - "node-04.anal.tools"
      - "node-05.anal.tools"
      - "node-04.anal.tools node-05.anal.tools"
    description: "PRODUCTION node(s) for deploy (using only when ENVIRONMENT=prod)"
  PRODUCTION_CRON_NODE:
    value: "node-04.anal.tools"
    options:
      - "node-04.anal.tools"
      - "node-05.anal.tools"
    description: "PRODUCTION node(s) for generate crontab file (using only when ENVIRONMENT=prod)"
  RUN_TESTS:
    value: "true"
    options:
      - "true"
      - "false"
    description: "Run tests"
  CRON_FILE_NAME:
    value: ${CI_PROJECT_NAME}-${ENVIRONMENT}
  DOCKER_BUILDKIT:
    value: '1'
  COMPOSE_DOCKER_CLI_BUILD:
    value: '1'
  SSH_AUTH_SOCK:
    value: "/tmp/1"
  RUN_MIGRATIONS:
    value: "true"
    options:
      - "true"
      - "false"
    description: "Run PG migrations on deployed node"
  SSH_CONNECT_PARAMS:
    value: "ssh -o StrictHostKeychecking=no -o PreferredAuthentications=publickey -o UserKnownHostsFile=/dev/null"  
  TIME:
    value: "/usr/bin/time -f '%E'"
  PHP_PATH:
    value: "/usr/bin/php${PHP_VERSION}"  
  LAST_SUCCESS_DEPLOYED_COMMIT_FILE:
    value: "/builds/last_success_deployed_commit_${ENVIRONMENT}"
  REMOVE_PGDATA_TESTS_VOLUME:
    value: "false"
    options:
      - "true"
      - "false"
    description: "Postgres docker volume (pgdata-test) should be deleted after the tests are completed"
  PIPELINE_NAME: "ENV: $ENVIRONMENT, $CI_COMMIT_MESSAGE"

workflow:
  name: '$PIPELINE_NAME'
  rules:
    - if: $ENVIRONMENT == "prod"
      variables:
        DEPLOY_ROOT: "/var/www/analytics"
        DEPLOY_NODE: "${PRODUCTION_DEPLOY_NODE}"
        MIGRATION_NODE: "node-04.anal.tools"
        SUPERRVISOR_WORKER_NAMES: "analytics-event-data analytics-event-send-events analytics-event-worker"
        CRON_NODE: ${PRODUCTION_CRON_NODE}

    - if: $ENVIRONMENT == "dev"
      variables:
        DEPLOY_ROOT: "/var/www/analytics-dev"
        DEPLOY_NODE: 'dev.anal.tools'
        MIGRATION_NODE: "${DEPLOY_NODE}"
        SUPERRVISOR_WORKER_NAMES: "analytics-event-data-dev analytics-event-send-events-dev analytics-event-worker-dev"
        CRON_NODE: "${DEPLOY_NODE}"

    - if: $CI_PIPELINE_SOURCE == "web"

.dind: &dind_template
  image: registry.anal.tools/analytics/analytics-docker-ci:latest
  tags:
    - core-analytics

Build Docker images:
  stage: build
  <<: *dind_template
  cache:  # Cache libraries in between jobs
    - key: cache-${ENVIRONMENT}-$(cat composer.lock composer.json | sha256sum)
      paths:
        - vendor/
      policy: pull-push
    - key: cache-${ENVIRONMENT}-$(cat package.json package-lock.json | sha256sum)
      paths:
        - web/
      policy: pull-push
  artifacts:
    paths:
      - vendor/
      - web/
  before_script:
    - |
      echo "Lists of commits in the pipeline from last successfully run to ${ENVIRONMENT} environment";
      printf "%b" "\e[1;35m\n====================================================================================\\n\n\e[0m"
      printf "%b" "\e[1;35m.\n.\n.\n.\n.\n\e[0m"
      if [[ ! -f "${LAST_SUCCESS_DEPLOYED_COMMIT_FILE}" ]]; then
         echo "Last deployed commit for ${ENVIRONMENT} environment not found (${LAST_SUCCESS_DEPLOYED_COMMIT_FILE} file not exsit)";
      else
         git log --oneline --no-abbrev-commit | grep `cat ${LAST_SUCCESS_DEPLOYED_COMMIT_FILE}` || ERR=$?
         if [[ ${ERR} -ne 0 ]]; then 
         echo "Commit `cat ${LAST_SUCCESS_DEPLOYED_COMMIT_FILE}` not found in the ${CI_COMMIT_BRANCH} branch"
         else
         git log --oneline --ancestry-path `cat ${LAST_SUCCESS_DEPLOYED_COMMIT_FILE}`..${CI_COMMIT_SHA} --pretty='%C(yellow)%cs %C(cyan)%s' --color=always
         fi ; 
      fi
      printf "%b" "\e[1;35m.\n.\n.\n.\n.\e[0m"
      printf "%b" "\e[1;35m\n====================================================================================\\n\n\e[0m"
  script:
      - echo "###DEBUG Deploying node is ${DEPLOY_NODE}, cronfile name is ${CRON_FILE_NAME}, deploy root is ${DEPLOY_ROOT}, migration node is ${MIGRATION_NODE}"
      - echo ${PWD}
      - if [ "${REMOVE_PGDATA_TESTS_VOLUME}" = "true" ]; then 
           docker volume rm -f analytics_pgdata-test;
        fi
      - ln -snf /usr/share/GeoIP ${PWD}/runtime/maxmind
      - ln -snf /usr/share/IP2Location ${PWD}/runtime/ip2location

      - ${TIME} docker compose --profile ci up --build -d
      - docker compose exec php bash 
        -c "echo "https://gitlab-ci-token:${CI_JOB_TOKEN}@git.slygods.com" > /home/<USER>/.git-credentials && 
        git config --global url."https://gitlab-ci-token:${CI_JOB_TOKEN}@git.slygods.com".insteadOf ssh://*******************:777"
      - docker compose exec npm bash 
        -c "echo "https://gitlab-ci-token:${CI_JOB_TOKEN}@git.slygods.com" > /home/<USER>/.git-credentials && 
        git config --global url."https://gitlab-ci-token:${CI_JOB_TOKEN}@git.slygods.com".insteadOf ssh://*******************:777"
      - ${TIME} ${PWD}/bin/composer install
      - if [ ! -f "${PWD}/tests/.env" ];
        then cp tests/.env.example tests/.env;
        fi
      - ${TIME} ${PWD}/bin/test-cli useragents/update 
      - ${TIME} ${PWD}/bin/test-cli migrate/up --non-interactive
      - ${TIME} ${PWD}/bin/test-cli cache/flush
      - ${TIME} ${PWD}/bin/npm i --save=false --no-progress
      - ${TIME} ${PWD}/bin/npm run build
      - if [ "${RUN_TESTS}" = "true" ]; then
           ${TIME} docker compose exec php vendor/bin/phpunit --do-not-cache-result;
        else
           echo "RUN_TESTS = ${RUN_TESTS}. Tests skipped";
        fi
 
  after_script:
      - docker compose --profile ci down

  rules:
    - if: $CI_PIPELINE_SOURCE == "web"

Deploy:
  stage: deploy
  <<: *dind_template
  before_script:
    - |
      eval $(ssh-agent -s)
      echo -n "$SSH_PRIVATE_KEY" | base64 -d | ssh-add -
      mkdir -p -m 700 ~/.ssh
      ssh-keyscan ${DEPLOY_NODE} >> ~/.ssh/known_hosts
      ssh-keyscan -p 777 git.slygods.com >> ~/.ssh/known_hosts
      echo "Adding .env file"
      git clone ssh://*******************:777/analytics/private/analytics-config.git /tmp/config
      rsync -avm --exclude=.git* /tmp/config/${ENVIRONMENT}/${CI_PROJECT_NAME}/ ./
  script:
      - echo "Deploy"
      - export RELEASE_SHORT=$(date +"%Y-%m-%dT%H-%M-%S")

      - for NODE in ${DEPLOY_NODE}; do rsync -apz
        -e "${SSH_CONNECT_PARAMS}"
        --chown=jenkins:www-data
        --chmod=D775,F664
        --exclude={.git*,.idea,docker*,bin,Dockerfile,README.md,Workflow.md,.dockerignore,runtime}
        ./ ${SSH_USER}@${NODE}:${DEPLOY_ROOT}/releases/${RELEASE_SHORT}; done

      - echo "Migrations PG"
      - ${TIME} bash -c 'if [ "${RUN_MIGRATIONS}" = "true" ]; then
           ${SSH_CONNECT_PARAMS} ${SSH_USER}@${MIGRATION_NODE} "
           ${PHP_PATH} ${DEPLOY_ROOT}/releases/${RELEASE_SHORT}/cli migrate/up --non-interactive";
        else
           echo "RUN_MIGRATIONS = ${RUN_MIGRATIONS}. Run migration on ${ENVIRONMENT} skipped";
        fi'

      - echo "Generating crontab file"
      - ${TIME} ${SSH_CONNECT_PARAMS} ${SSH_USER}@${CRON_NODE} "
        ${PHP_PATH} ${DEPLOY_ROOT}/releases/${RELEASE_SHORT}/cli crontab > /tmp/${CRON_FILE_NAME}.crontab; 
        sudo rsync --chown=root:root --remove-source-files /tmp/${CRON_FILE_NAME}.crontab /etc/cron.d/${CI_PROJECT_NAME}_${ENVIRONMENT}"

      - echo "Switch release"
      - for NODE in ${DEPLOY_NODE}; do ${SSH_CONNECT_PARAMS} ${SSH_USER}@${NODE} "
        [ -f ${DEPLOY_ROOT}/releases/${RELEASE_SHORT}/cli ] && chmod +x ${DEPLOY_ROOT}/releases/${RELEASE_SHORT}/cli &&
        rm -rf ${DEPLOY_ROOT}/releases/${RELEASE_SHORT}/runtime &&
        sudo ln -snf ${DEPLOY_ROOT}/shared/runtime ${DEPLOY_ROOT}/releases/${RELEASE_SHORT}/runtime &&
        sudo ln -snf ${DEPLOY_ROOT}/releases/${RELEASE_SHORT} ${DEPLOY_ROOT}/current &&
        sudo -u www-data ${PHP_PATH} ${DEPLOY_ROOT}/current/cli cache/flush &&
        sudo -u www-data ${PHP_PATH} ${DEPLOY_ROOT}/current/cli task/stop-all-peons &&
        sudo /usr/local/bin/cachetool --config /etc/cachetool/analytics.yaml  opcache:reset -vvv --no-interaction"; done

      - echo "${CI_COMMIT_SHA}" > ${LAST_SUCCESS_DEPLOYED_COMMIT_FILE}
  rules:
    - if: $CI_PIPELINE_SOURCE == "web"
  after_script:
    - rm -rf $CI_PROJECT_DIR
