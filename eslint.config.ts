import { globalIgnores } from 'eslint/config'
import { defineConfigWithVueTs, vueTsConfigs } from '@vue/eslint-config-typescript'
import pluginVue from 'eslint-plugin-vue'
export default defineConfigWithVueTs(
    {
        name: 'app/files-to-lint',
        files: ['**/*.{ts,mts,tsx,vue}'],
    },

    globalIgnores(['**/dist/**', '**/dist-ssr/**', '**/coverage/**']),

    pluginVue.configs['flat/essential'],
    pluginVue.configs["flat/strongly-recommended"],
    pluginVue.configs['flat/recommended'],
    vueTsConfigs.recommended,
    {
        rules: {
            'vue/script-indent': ['warn', 4, {switchCase: 1}],
            'vue/html-indent': ['warn', 4, {attribute: 1, baseIndent: 1}],
            'vue/no-v-html': 'off',
            'vue/attribute-hyphenation': 'off',
            'vue/multi-word-component-names': 'off',
            'vue/v-on-event-hyphenation': 'off',
            'vue/no-unused-components': 'warn',
            'vue/no-unused-vars': 'warn',
            'vue/max-attributes-per-line': ['error', {"singleline": {"max": 2}}],
            'vue/singleline-html-element-content-newline': 'off',
            'vue/component-name-in-template-casing': ["error", "PascalCase"],
            'multiline-ternary': 'off',
            'no-unused-vars': 'off',
            'no-multiple-empty-lines': 'warn',
            'comma-dangle': ['warn', 'always-multiline'],
            'no-console': 'warn',
            'no-debugger': 'warn',
            'vue/require-v-for-key': 'off',
            '@typescript-eslint/no-unused-vars': 'off', // Disables errors of fake unused types, that used only for Template highlight: /front/apps/back/employees/index.vue:105  error  'EmployeeRow' is defined but never used
            '@typescript-eslint/no-non-null-assertion': 'off', // For Vue refs: const $el = ref<HTMLElement | null>(null); $el.value!.innerHTML = 'qwe'
            '@typescript-eslint/no-dynamic-delete': 'off',
        },
    },
)
