{"name": "an/analytics", "description": "Evoplay analytics", "type": "project", "minimum-stability": "dev", "prefer-stable": true, "authors": [{"name": "Becalm", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>man", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "Sartor", "email": "<EMAIL>", "role": "Tech lead"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Lead developer"}], "require": {"php": "^8.3", "ext-bcmath": "*", "ext-ctype": "*", "ext-curl": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-imagick": "*", "ext-intl": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-pcntl": "*", "ext-pdo": "*", "ext-pgsql": "*", "ext-posix": "*", "ext-redis": "*", "ext-sockets": "*", "ext-sodium": "*", "ext-xml": "*", "ext-zip": "*", "aws/aws-sdk-php": "^3.317", "cebe/markdown": "^1.2", "cmn/useragent-rules": "^1.6.5", "dragonmantank/cron-expression": "^3.3", "google/apiclient": "^v2.17.0", "google/cloud-bigquery": "^v1.30.2", "ip2location/ip2location-php": "^9.7", "kriswallsmith/buzz": "^1.2", "league/container": "^4.2", "maxmind-db/reader": "^1.11", "monolog/monolog": "^2.9", "nikic/fast-route": "^1.3", "nyholm/psr7": "^1.8", "predis/predis": "^2.3", "sentry/sentry": "^4.10.0", "symfony/filesystem": "^v7.1", "symfony/http-client": "^v7.1", "symfony/http-foundation": "^v7.1", "symfony/mailer": "^v7.1", "symfony/process": "^7.1", "symfony/yaml": "^7.1", "ua-parser/uap-php": "dev-bugfix-null-handling-in-yaml", "vlucas/phpdotenv": "^5.6", "yiisoft/cache": "^3.0", "yiisoft/cache-file": "^3.1", "yiisoft/db": "^1.3", "yiisoft/db-pgsql": "dev-fix-php-8.4-deprecations"}, "require-dev": {"squizlabs/php_codesniffer": "^3.9.0", "phpunit/phpunit": "^11.4"}, "repositories": [{"type": "vcs", "url": "ssh://*******************:777/analytics/public/useragent-rules.git"}, {"type": "vcs", "url": "https://github.com/sartor/uap-php"}, {"type": "vcs", "url": "https://github.com/sartor/db"}, {"type": "vcs", "url": "https://github.com/sartor/db-pgsql"}], "autoload": {"psr-4": {"app\\": ""}}, "scripts": {"pre-autoload-dump": ["Google\\Task\\Composer::cleanup", "Aws\\Script\\Composer\\Composer::removeUnusedServices"]}, "extra": {"google/apiclient-services": ["Drive"], "aws/aws-sdk-php": ["S3"]}, "config": {"allow-plugins": {"php-http/discovery": true}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-intl-idn": "*", "symfony/polyfill-intl-normalizer": "*", "symfony/polyfill-mbstring": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php80": "*", "symfony/polyfill-php81": "*", "symfony/polyfill-php83": "*"}}