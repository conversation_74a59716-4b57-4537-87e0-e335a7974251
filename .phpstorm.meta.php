<?php
namespace PHPSTORM_META {
    override(\League\Container\Container::get(), type(0));
    override(\League\Container\Container::getNew(), type(0));
    override(\app\back\components\AllowedLists::repo(), type(0));
    override(\app\back\components\Container::create(), type(0));
    override(\app\back\components\RepositoryFactory::create(), type(0));
    override(\app\tests\libs\DbTransactionalUnitTrait::repo(), type(0));
    override(\app\tests\libs\NewFakerUnitTrait::ruleForCheck(), type(0));
}
