<template>
    <card width="wide">
        <rich-table
            v-bind="table"
            showRefresh
            showPagination
            :reloadOnChange="false"
            @reload="onReload"
            @change="onChange"
        >
            <template #user="{row}: {row: CheckUserTableRow}">
                <a
                    :href="`/user/player/${row.siteId}-${row.userId}`"
                    target="_blank"
                >{{ row.siteUser }}</a>
            </template>
            <template #checks="{row}: {row: CheckUserTableRow}">
                <template v-for="check in row.checks">
                    <span
                        role="button"
                        class="badge cursor-pointer me-1 text-wrap"
                        :class="{'bg-success': check.score > 0, 'bg-danger': check.score < 0}"
                        :title="showMessage ? check.name : check.message"
                        @click="showMessage = !showMessage"
                    >{{ showMessage ? check.message : check.name }}</span>
                </template>
            </template>
        </rich-table>
    </card>
</template>

<script lang="ts">
import { RichTable } from '@/components'
import { Card } from '@/widgets'
import { defineComponent } from 'vue'
import { Values, NextWithReload, RichTableType } from '@/types'

interface CheckUserTableRow {
    siteId: number
    userId: number
    siteUser: string
    checks: {
        name: string
        score: number
        message: string
    }[]
}

export default defineComponent({
    components: {
        Card,
        RichTable,
    },
    beforeRouteEnter (to, _from, next: NextWithReload) {
        next(vm => vm.reload(vm.$decodeParams(to.query)))
    },
    data () {
        return {
            table: {
                richTable: {},
            } as RichTableType,
            showMessage: false,
        }
    },
    methods: {
        backUrl (postfix: string) {
            return `/checks/users-checks${postfix}`
        },
        onChange (params: Values) {
            this.table!.form!.values = params
            this.$historyReplaceParams(params)
        },
        onReload (params: Values) {
            return this.$processRichTableResponse(this.$fetch(this.backUrl('/data'), params), this.table)
        },
        async reload (params: Values) {
            this.table.form = await this.$fetch(this.backUrl('/form'), params)
        },
    },
})
</script>
