<template>
    <Card>
        <RichTable
            v-bind="richTable"
            :show-total="false"
            @reload="reload"
        >
            <template #daily_sum="{row, refreshCallback}">
                <InplaceEdit
                    :value="row.daily_sum"
                    @submit="submitDailySum({dailySum: $event, siteId: row.site_id}, refreshCallback)"
                />
            </template>
            <template #progress="{row}">
                <ProgressBar
                    :progress="row.progress"
                />
            </template>
        </RichTable>
    </Card>
</template>

<script lang="ts">
import { RichTable, InplaceEdit, ProgressBar } from '@/components'
import { NextWithReload, Values } from '@/types'
import { Card } from '@/widgets'
import { defineComponent } from 'vue'

export default defineComponent({
    components: {
        Card,
        RichTable,
        InplaceEdit,
        ProgressBar,
    },
    beforeRouteEnter (to, _from, next: NextWithReload) {
        next(vm => vm.reload(vm.$decodeParams(to.query)))
    },
    data () {
        return {
            richTable: {},
        }
    },
    methods: {
        reload () {
            this.$processRichTableResponse(this.$fetch(this.$route.path + '/data'), this.richTable)
        },
        submitDailySum (params: Values, refreshCallback: () => void) {
            this.$fetch(this.$route.path + '/update-daily-limit-sum', params).then(refreshCallback)
        },
    },
})
</script>
