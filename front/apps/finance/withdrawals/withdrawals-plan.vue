<template>
    <template v-if="initialization">
        Loading...
    </template>
    <template v-else>
        <RichTable
            class="table table-withdrawal-plan"
            :tableClasses="{ 'table-striped': false }"
            size="sm"
            title=""
            :showTotal="false"
            :reloadOnChange="false"
            v-bind="withdrawalsTableExtended"
            @reload="load"
            @change="change"
        >
            <template #remove="{row}: {row: WithdrawalsPlanTableRow}">
                <div class="text-center">
                    <Icona
                        v-if="row?.aborted_by !== false"
                        name="icn-ban"
                        class="text-danger"
                        :title="`Aborted by ${row?.aborted_by}`"
                    />
                    <button
                        v-else-if="row?.id"
                        type="button"
                        class="btn btn-sm btn-warning px-1 py-0"
                        title="Remove from plan"
                        @click="abortPlannedWithdrawals([row?.id])"
                    >
                        <Icona name="icn-delete" />
                    </button>
                </div>
            </template>
        </RichTable>
        <div class="row">
            <div class="col-6">
                <button
                    class="btn btn-warning"
                    :disabled="!abortAvailable"
                    @click="abortAllPlanned"
                >
                    Abort planned
                </button>
            </div>
            <div class="col-6">
                <button
                    class="btn btn-success"
                    :disabled="!confirmAvailable"
                    @click="confirm"
                >
                    Schedule
                </button>
            </div>
        </div>
    </template>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
import { RichTableType, TableRow, Values } from '@/types'
import { Icona, RichTable } from '@/components'

class AbortLoadingError extends Error {
}

interface WithdrawalsPlanTableRow extends TableRow {
    id: number | null
    aborted_by: string | false
    transactionId: string
    createdAt: string
    plannedAt: string
    sum: string
}

export default defineComponent({
    components: {
        Icona,
        RichTable,
    },
    inheritAttrs: false,
    props: {
        siteUser: {
            type: Object,
            default: () => ({}),
        },
    },
    emits: ['change', 'submit'],
    data () {
        return {
            confirmAvailable: false,
            abortAvailable: false,
            initialization: true,
            transactionsTable: { } as RichTableType & { table: { data: WithdrawalsPlanTableRow[] } },
            loadingSiteUser: null as string | null,
        }
    },
    computed: {
        withdrawalsTableExtended () {
            const result = Object.assign({}, this.transactionsTable) as RichTableType

            if (result.table) {
                result.table.rowProps = this.rowProps
            }

            return result
        },
    },
    watch: {
        siteUser: {
            immediate: true,
            handler () {
                this.load()
            },
        },
    },
    methods: {
        rowProps ({ id, aborted_by }: TableRow) {
            return {
                class: {
                    'text-primary': id && aborted_by === false,
                    'text-danger': id && aborted_by !== false,
                },
            }
        },
        load (filters = {}) {
            this.confirmAvailable = false
            if (this.transactionsTable.table) {
                this.transactionsTable.table.data = []
            }
            if (Object.keys(filters).length === 0) {
                this.initialization = true
            }
            const siteUserStr = this.siteUser.siteId + '-' + this.siteUser.userId
            this.loadingSiteUser = siteUserStr
            const params = Object.assign({}, this.siteUser, { limitCount: 1 }, filters)
            return this.$processRichTableResponse(
                this.$fetch('/finance/withdrawals/withdrawals-plan', params).then((table: RichTableType) => {
                    if (siteUserStr !== this.loadingSiteUser) {
                        throw new AbortLoadingError()
                    }
                    return table
                }),
                this.transactionsTable,
            ).then(() => {
                this.confirmAvailable = this.transactionsTable.table.data.some((row: WithdrawalsPlanTableRow) => row.id === null) || false
                this.abortAvailable = this.transactionsTable.table.data.some((row: WithdrawalsPlanTableRow) => row.id && !row.aborted_by) || false
                this.initialization = false
            }).catch((e: Error) => {
                if (!(e instanceof AbortLoadingError)) {
                    throw e
                }
            })
        },
        change (values: Values) {
            if (this.transactionsTable.form?.values) {
                Object.assign(this.transactionsTable.form.values, values)
            }
        },
        abortAllPlanned () {
            const plannedWithdrawals = this.transactionsTable.table.data
                .filter((row: WithdrawalsPlanTableRow) => row.id && !row.aborted_by)
                .map(row => row.id as number)

            this.abortPlannedWithdrawals(plannedWithdrawals)
                .catch(() => this.abortAvailable = true)
        },
        confirm () {
            this.confirmAvailable = false
            if (! this.transactionsTable.table?.data) {
                return
            }

            const plan = Object.fromEntries(this.transactionsTable.table.data
                .filter((row: WithdrawalsPlanTableRow) => !row.id)
                .map((row: WithdrawalsPlanTableRow) => [row.transactionId as string, row.plannedAt]))

            this.$fetch('/finance/withdrawals/withdrawals-plan-submit', { ...this.siteUser, plan })
                .then(() => this.$emit('submit'))
                .catch(() => this.confirmAvailable = true)
        },
        abortPlannedWithdrawals (id: number[]) {
            return this.$fetch('/finance/withdrawals/withdrawals-plan-delete', { id })
                .then(() => this.load(this.transactionsTable.form?.values))
                .then(() => this.$emit('change'))
        },
    },
})
</script>
<style lang="scss">
.table-withdrawal-plan td {
    padding-left: 5px;
    padding-right: 5px;
}
</style>
