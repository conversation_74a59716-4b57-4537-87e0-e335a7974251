<template>
    <RichTable
        class="withdrawals-table table-hover"
        showPagination
        showRefresh
        sticky
        v-bind="withdrawalsTableExtended"
        @reload="onReload"
    >
        <template #afterTitle="{refreshCallback}">
            <button
                class="btn btn-secondary btn-sm"
                @click="openPopup('undo', {refreshCallback})"
            >
                <Icona name="icn-undo" /> Undo
            </button>
            <template v-if="selectedForDecision.length > 0">
                <button
                    class="btn btn-success btn-sm ms-2"
                    :disabled="withdrawalsTableExtended.disabled"
                    @click="withdrawSelectedProcess('allow', refreshCallback)"
                >
                    <Icona name="icn-thumb-up" /> Approve selected
                </button>
                <button
                    ref="denySelected"
                    class="btn btn-danger btn-sm ms-2"
                    :disabled="withdrawalsTableExtended.disabled"
                    @click="withdrawSelectedProcess('deny', refreshCallback)"
                >
                    <Icona name="icn-thumb-down" /> Reject selected
                </button>
            </template>
            <button
                v-if="selectedPlanned.length > 0"
                class="btn btn-warning btn-sm ms-2"
                :disabled="withdrawalsTableExtended.disabled"
                @click="abortSelectedPlan(refreshCallback)"
            >
                <Icona name="icn-ban" /> Abort plan
            </button>
        </template>

        <template #userId="{row: {userId, siteId, isIgnored, ignoreComment, userCommentWithdraw, userCommentWithdrawUpdatedBy}, refreshCallback}: {row: TransactionRow, refreshCallback: () => void}">
            <Icona
                v-if="isIgnored"
                name="icn-spy"
                class="text-danger"
                :title="ignoreComment"
            />
            <Icona
                v-if="userCommentWithdraw && userCommentWithdrawUpdatedBy"
                name="icn-question"
                class="me-1"
                :title="userCommentWithdraw + ' (' + userCommentWithdrawUpdatedBy + ' )'"
            />
            <Icona
                v-else-if="userCommentWithdraw"
                name="icn-message-exclamation"
                class="me-1"
                :title="userCommentWithdraw"
            />
            <a
                href="javascript:void(0)"
                @click="openPopup('user', {refreshCallback, siteUser: {userId, siteId}})"
            >{{ userId }}</a>
        </template>

        <template #userLinks="{row: {links}}: {row: TransactionRow}">
            <div class="text-nowrap">
                <a
                    v-for="{href, icon} in links"
                    class="ps-1"
                    target="_blank"
                    :href="href"
                >
                    <Icona :name="`${icon}`" />
                </a>
            </div>
        </template>

        <template #withdrawSum="{row}: {row: TransactionRow}">
            {{ row.sumFull }}

            <template v-if="row.countTotal > 1">
                ({{ row.countTotal }})
            </template>

            <Icona
                v-if="row.countAllowed > 0 || row.countDenied > 0"
                name="icn-question"
                :title="row.processedTitle"
            />
        </template>

        <template #balance="{row}: {row: TransactionRow}">
            {{ row.balance }}
        </template>

        <template #inOut="{row}: {row: TransactionRow}">
            {{ row.depLtEur }} / {{ row.wdLtEur }}
            <Icona
                v-if="row.isWdGreaterThanDep"
                name="icn-exclamation-triangle"
                size="lg"
                class="text-danger"
                title="Withdrawals sum > IN"
            />
        </template>

        <template #select="{row}: {row: TransactionRow}">
            <div
                v-if="row.canAllow || row.canDeny"
                class="form-check form-check-inline"
            >
                <input
                    :id="'select-row-' + row.rowId"
                    v-model="selectedRows"
                    class="form-check-input"
                    title="Select for batch approve or decline"
                    type="checkbox"
                    :value="row.rowId"
                >
            </div>
        </template>

        <template #withdraw="{row, refreshCallback}: {row: TransactionRow, refreshCallback: () => void}">
            <div class="input-group input-group-sm">
                <button
                    :class="['plannedWithdrawalIds' in row && Object.keys(row.plannedWithdrawalIds).length > 0 ? 'btn-warning' : 'btn-secondary']"
                    class="btn btn-sm"
                    title="Schedule a transaction"
                    @click.stop="onPlanOpen(() => $event.target as Element, row?.siteId, row?.userId, row?.siteUser)"
                >
                    <Icona
                        name="icn-hourglass"
                        class="d-inline-block"
                    />
                </button>
                <template v-if="row.combinations.length === 1">
                    <input
                        class="form-control form-control-sm"
                        readonly
                        :value="row.combinations[0].name"
                        title="Amount"
                    >
                </template>
                <select
                    v-else
                    class="form-select form-select-sm"
                    title="Amount"
                    :disabled="!row.combinations.length"
                    @change="setWithdrawalValue(($event.target as HTMLSelectElement).value, row)"
                >
                    <option
                        v-for="comb in row.combinations"
                        :value="comb.id"
                        :selected="selectedTransactions[row.rowId] === comb.id"
                    >
                        {{ comb.name }}
                    </option>
                </select>
                <button
                    v-if="row.canAllow"
                    class="btn btn-success"
                    :disabled="withdrawalsTableExtended.disabled || !row.combinations.length"
                    @click="withdrawAllow(row, refreshCallback)"
                >
                    <Icona name="icn-check" />
                </button>
            </div>
        </template>

        <template #wallet="{row}: {row: TransactionRow}">
            <Icona
                v-if="row.hasBlockedRequisites"
                name="icn-credit-card-off"
                size="lg"
                class="text-danger me-1"
                title="User has blocked requisite(s)"
            />
            <router-link
                v-if="row.walletReportUrl"
                :to="row.walletReportUrl"
                target="_blank"
            >
                {{ row.wallet }}
            </router-link>
            <template v-else>
                {{ row.wallet }}
            </template>
        </template>

        <template #comment="{row: {comment, siteId, userId, commentWithdrawUpdatedBy}, refreshCallback}: {row: TransactionRow, refreshCallback: () => void}">
            <InplaceEdit
                :value="comment"
                :disabled="withdrawalsTableExtended.disabled"
                :title="commentWithdrawUpdatedBy ? commentWithdrawUpdatedBy : ''"
                @submit="submitComment({comment: $event, siteId, userId}, refreshCallback)"
            />
        </template>

        <template #btnDeny="{row}: {row: TransactionRow}">
            <button
                v-if="row.canDeny"
                class="btn btn-danger btn-sm"
                :disabled="withdrawalsTableExtended.disabled || !row?.combinations?.length"
                @click="withdrawDeny(() => $event.target as Element, row)"
            >
                <Icona name="icn-ban" />
            </button>
            <template v-else>
                -
            </template>
        </template>
    </RichTable>
    <PopoverSingleton
        :popoverTargetClosure="planButtonElementClosure"
        :title="`Withdrawal plan ${popoverTargetSiteUser}`"
        position="left"
        @close="onPlanClose"
    >
        <WithdrawalsPlan
            v-bind="withdrawalPlanParams"
            @change="onPlanChange"
            @submit="onPlanSubmit"
        />
    </PopoverSingleton>
    <PopoverSingleton
        v-if="denySelectedTarget !== undefined"
        :popoverTargetClosure="() => $refs.denySelected"
        :wide="false"
        @close="denySelectedTarget = undefined"
    >
        <FormGrid
            v-bind="denySelectedTarget?.form"
            @change="denySelectedTarget!.send"
        />
    </PopoverSingleton>
</template>

<script lang="ts">
import { RichTable, InplaceEdit, PopoverSingleton, Icona, FormGrid } from '@/components'
import { defineComponent } from 'vue'
import { FormGridType, RichTableType, Values } from '@/types'
import { PopupParams } from './withdrawals.vue'
import WithdrawalsPlan from './withdrawals-plan.vue'
import { DenyTargetType } from './deny-popover.vue'

interface Combination {
    id: string
    name: string
}

interface SiteIdUserId {
    siteId: number
    userId: number
}

interface TransactionRow extends SiteIdUserId {
    rowId: string
    siteId: number
    userId: number
    brandId: number | null
    psName: string
    siteUser: string // "CV-627698"
    currency: string
    countTotal: number
    sumFull: number
    statsCurrency: string
    balance: string
    depLtEur: string
    wdLtEur: string
    siteName: string
    userFullStatus: string
    cid: number | null
    kyc: string | null
    combinations: Combination[]
    countAllowed: number
    countDenied: number
    sumAllowed: number
    sumDenied: number
    processedTitle?: string
    canAllow: boolean
    canDeny: boolean
    isIgnored: boolean
    ignoreComment: string
    isWdGreaterThanDep: boolean
    wallet: string
    walletReportUrl?: string
    hasBlockedRequisites: boolean
    comment: string
    commentWithdrawUpdatedBy: string
    userCommentWithdraw: string
    userCommentWithdrawUpdatedBy: string
    plannedStats: Record<string, number>
    plannedWithdrawalIds: number[]
    links: {
        href: string
        icon: string
    }[]
}

export interface DecisionData extends SiteIdUserId {
    transactionIds: string[]
    reason?: number
}

export default defineComponent({
    components: {
        FormGrid,
        Icona,
        InplaceEdit,
        PopoverSingleton,
        RichTable,
        WithdrawalsPlan,
    },

    emits: ['deny', 'openPopup'],

    data: function () {
        return {
            denySelectedTarget: undefined as undefined | {
                form: FormGridType<DecisionData>,
                send: (values: Values) => void,
            },
            withdrawalPlanParams: {} as Record<string, Record<string, number>>,
            withdrawalsTable: {} as RichTableType & { table: { data: TransactionRow[] } },
            selectedRows: [] as string[],
            selectedTransactions: {} as Record<string, string>,
            planButtonElementClosure: undefined as (() => Element) | undefined,
            denyButtonElementClosure: null as (() => Element) | null,
            popoverTargetSiteUser: '' as string,
            popoverPlanChanged: false,
            rows: {} as Record<string, TransactionRow>,
        }
    },

    computed: {
        withdrawalsTableExtended (): RichTableType {
            const result = Object.assign({}, this.withdrawalsTable)

            if (result.table) {
                result.table.rowProps = this.rowProps
            }

            return result
        },
        selectedTransactionsWithDefault () {
            const result = Object.assign({}, this.selectedTransactions)
            Object.entries(this.rows).forEach(([id, row]) => {
                if (row?.combinations?.length > 0 && !(id in this.selectedTransactions)) {
                    result[id] = row.combinations[0].id
                }
            })
            return result
        },
        selectedForDecision () {
            return this.selectedRows.filter(id => this.rows[id]?.combinations?.length > 0)
        },
        selectedPlanned () {
            return this.selectedRows.filter(id => this.rows[id]?.plannedWithdrawalIds?.length > 0)
        },
    },
    methods: {
        async reload (params: Values) {
            await this.$processRichTableResponse(this.$fetch(this.$route.path + '/withdrawals-data', params), this.withdrawalsTable)
            this.rows = {}
            this.withdrawalsTable.table.data.forEach((row: TransactionRow) => {
                row.processedTitle = this.formatTransactionsTitle(row)
                row.rowId = `${row.siteId}-${row.userId}`
                this.rows[row.rowId] = row
            })
            this.withdrawalsTable.table!.rowKeyName = 'rowId'
        },
        async onReload (params: Values) {
            this.$historyReplaceParams(params)
            await this.reload(params)
        },
        async reloadWithPagination () {
            await this.reload({
                ...this.withdrawalsTable.form?.values,
                ...this.withdrawalsTable.pagination,
            })
        },
        deselectRow (rowId: string) {
            const i = this.selectedRows.indexOf(rowId)
            if (i > -1) {
                this.selectedRows.splice(i, 1)
            }
        },
        getWithdrawalValue (row: TransactionRow) {
            return this.selectedTransactionsWithDefault[row.rowId].split(',')
        },
        setWithdrawalValue (transactionIds: string, row: TransactionRow): void {
            this.selectedTransactions[row.rowId] = transactionIds
        },
        formatTransactionsTitle ({ countAllowed, sumAllowed, currency, sumDenied, countDenied }: TransactionRow) {
            let res = ''
            if (countAllowed > 0) {
                res += `Allowed ${countAllowed} transaction(s) for ${sumAllowed} ${currency}`
            }
            if (countDenied > 0) {
                res += `Denied ${countDenied} transaction(s) for ${sumDenied} ${currency}`
            }

            return res
        },
        submitComment (params: Values, refreshCallback: () => void) {
            return this.$fetch(this.$route.path + '/update-withdraw-comment', params)
                .then(refreshCallback)
        },
        async withdrawAllow (row: TransactionRow, refreshCallback: () => void) {
            return this.submitWithdrawDecision('allow', { siteId: row.siteId, userId: row.userId, transactionIds: this.getWithdrawalValue(row) }, refreshCallback)
                .then(() => this.deselectRow(row.rowId))
        },
        withdrawDeny (targetButton: () => Element, row: TransactionRow) {
            const data: DenyTargetType = {
                siteUser: row.siteUser,
                siteId: row.siteId,
                userId: row.userId,
                transactionIds: this.getWithdrawalValue(row),
                denyButtonElementClosure: targetButton,
                requestPromiseCallback: async (requestPromise) => {
                    this.withdrawalsTable.disabled = true
                    try {
                        await requestPromise
                        await this.reloadWithPagination()
                        this.deselectRow(row.rowId)
                    } finally {
                        this.withdrawalsTable.disabled = false
                    }
                },
            }
            this.$emit('deny', data)
        },
        withdrawSelectedProcess (decision: string, refreshCallback: () => void) {
            const sumProcess: Record<string, number> = {}
            const decisionData = {} as Record<string, DecisionData>
            this.selectedForDecision.map(id => ({ siteUser: id, row: this.rows[id] })).forEach(({ siteUser, row }) => {
                const selectedRowTransactions = this.selectedTransactionsWithDefault[siteUser]
                if (!(row.currency in sumProcess)) {
                    sumProcess[row.currency] = 0
                }
                // Only one selected combination of transaction ids per user
                const combination: Combination | undefined = row.combinations.find(c => c.id === selectedRowTransactions)
                if (combination !== undefined) {
                    decisionData[siteUser] = { siteId: row.siteId, userId: row.userId, transactionIds: this.getWithdrawalValue(row) }
                    sumProcess[row.currency] += parseInt(combination.name.replace(/\D/, ''))
                }
            })

            const currencies = Object.entries(sumProcess).reduce((acc, [currency, amount]) => acc + ', ' + amount + ' ' + currency, '')
            const decisionInfo = decision + ': ' + this.selectedRows.length + ' users' + currencies

            if (decision === 'deny') {
                return this.withdrawDenySelectedReasonFormLoad(decisionInfo, (reason: number) => {
                    Object.values(decisionData).forEach(data => {
                        data.reason = reason
                    })
                    return this.withdrawDecisionSelectedSend(decision, decisionData, refreshCallback)
                })
            } else if (confirm('Are you sure? ' + decisionInfo)) {
                this.withdrawDecisionSelectedSend(decision, decisionData, refreshCallback)
            }
        },
        async withdrawDenySelectedReasonFormLoad (title: string, sendCallback: (reason: number) => Promise<void>) {
            const denyForm = await this.$fetch(this.$route.path + '/deny-form')
            denyForm.title = title
            this.denySelectedTarget = {
                richTable: denyForm,
                send: (values) => {
                    this.denySelectedTarget!.form.enabled = false
                    this.withdrawalsTable.disabled = true
                    sendCallback(values.reason as number).finally(() => {
                        this.denySelectedTarget = undefined
                        this.withdrawalsTable.disabled = false
                    })
                },
            }
        },
        async withdrawDecisionSelectedSend (decision: string, decisionData: Record<string, DecisionData>, refreshCallback: () => void) {
            for (const [siteUser, siteUserDd] of Object.entries(decisionData)) {
                try {
                    await this.submitWithdrawDecision(
                        decision,
                        siteUserDd,
                        () => {
                            this.deselectRow(siteUser)
                            this.$notify({
                                type: 'success',
                                message: 'User ' + siteUser + ' processed',
                            })
                        },
                    )
                } catch { /* continue sending */ }
            }
            refreshCallback()
        },

        submitWithdrawDecision (decision: string, data: DecisionData, refreshCallback: () => void) {
            return this.$fetch(this.$route.path + '/' + decision, data).then(refreshCallback)
        },

        abortSelectedPlan (refreshCallback: () => void) {
            const usersWithPlan = Object.fromEntries(this.selectedPlanned.map(id => [id, [
                this.rows[id].currency,
                Object.values(this.rows[id].plannedStats).reduce((sum, cur) => sum + parseFloat('' + cur), 0) as number,
            ]]))
            const sumProcess = {} as Record<string, number>
            Object.values(usersWithPlan).forEach(([currency, sum]) => sumProcess[currency] = (sumProcess[currency] || 0) + parseFloat('' + sum))
            const currencies = Object.entries(sumProcess).reduce((acc, [currency, amount]) => acc + ', ' + parseInt(amount + '') + ' ' + currency, '')

            if (!confirm('Abort plan? ' + this.selectedPlanned.length + ' users' + currencies)) {
                return
            }

            this.$fetch(this.$route.path + '/withdrawals-plan-delete', {
                id: this.selectedPlanned.map(id => this.rows[id].plannedWithdrawalIds).flat(),
            }).then(refreshCallback)
        },

        rowProps ({ countDenied, countAllowed, userCommentWithdraw }: TransactionRow) {
            const classes: Record<string, boolean> = {}

            if (countAllowed > 0) {
                classes['table-success'] = true
            }

            if (countDenied > 0) {
                classes['table-danger'] = true
            }

            if (userCommentWithdraw) {
                classes['user-has-comment'] = true
            }

            return { class: classes }
        },

        openPopup (name: string, params: PopupParams) {
            this.$emit('openPopup', { name, params })
        },

        onPlanOpen (el: () => Element, siteId: number, userId: number, siteUser: string) {
            if (this.planButtonElementClosure !== undefined && this.popoverPlanChanged) {
                this.onPlanClose()
            }
            this.popoverTargetSiteUser = siteUser
            this.popoverPlanChanged = false
            this.planButtonElementClosure = el
            this.withdrawalPlanParams = { siteUser: { siteId, userId } }
        },

        onPlanClose () {
            this.withdrawalPlanParams = {}
            this.planButtonElementClosure = undefined
            if (this.popoverPlanChanged) {
                this.reloadWithPagination()
            }
        },

        onPlanChange () {
            this.popoverPlanChanged = true
        },

        onPlanSubmit () {
            this.onPlanChange()
            this.onPlanClose()
        },
    },
})
</script>

<style lang="scss">
.withdrawals-table {
    tr {
        &.user-has-comment {
            td {
                font-weight: bold;
            }
        }
    }
}
</style>
