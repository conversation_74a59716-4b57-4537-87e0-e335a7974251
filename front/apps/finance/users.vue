<template>
    <Card>
        <RichTable
            v-bind="richTable"
            showPagination
            :reloadOnChange="false"
            @change="richTable.form.values = $event"
            @reload="onReload"
        >
            <template #commentWithdraw="{row}">
                <Popover
                    :title="`User comments ${row.short_name}-${row.user_id}`"
                    hideOnOutside
                    position="left"
                    @open="onOpenComments(row)"
                >
                    <span
                        class="inplace-edit-link"
                        :title="'Add comment or show history'"
                    >
                        {{ row.comment_withdraw || 'empty' }}
                    </span>
                    <template v-if="siteUser" #content>
                        <Comments
                            v-if="commentsPopupOpened"
                            :siteUser="siteUser"
                        />
                    </template>
                </Popover>
            </template>

            <template #action="{row}">
                <button
                    class="btn btn-primary btn-sm"
                    @click="onOpenTransactions(row)"
                >
                    <Icona name="icn-eye" /> Transactions
                </button>
            </template>
        </RichTable>

        <!--Modal window-->
        <Popup
            width="full"
            :opened="transactionsPopupOpened"
            @close="transactionsPopupOpened = false"
        >
            <UserTransactions
                v-if="transactionsPopupOpened"
                :site-user="siteUser"
                @deny="denyTarget = $event"
            />
        </Popup>

        <DenyPopover :denyTarget="denyTarget" />
    </Card>
</template>

<script lang="ts">
import { Icona, Popover, Popup, RichTable } from '@/components'
import { UserTransactions, Card, Comments } from '@/widgets'
import { defineComponent } from 'vue'
import { FormGridType, NextWithReload, Values } from '@/types'
import DenyPopover, { DenyTargetType } from '@/apps/finance/withdrawals/deny-popover.vue'

interface FinanceUsersTableRow {
    site_id: number
    user_id: number
    site_name: string
    short_name: string
    date: string
    balance: string
    dep_lt_rub: string
    wd_lt_rub: string
    comment_withdraw: string
}

export default defineComponent({
    components: {
        Popover,
        DenyPopover,
        Icona,
        Card,
        UserTransactions,
        Popup,
        RichTable,
        Comments,
    },

    beforeRouteEnter (to, _from, next: NextWithReload) {
        next(vm => vm.reload(vm.$decodeParams(to.query)))
    },

    data () {
        return {
            richTable: {
                form: {} as FormGridType,
            },
            siteUser: undefined as {
                siteId: number,
                userId: number
            } | undefined,
            denyTarget: undefined as undefined | DenyTargetType,
            commentsPopupOpened: false,
            transactionsPopupOpened: false,
        }
    },

    methods: {
        reload (params: Values) {
            const isEmptyParams = Object.keys(params).length === 0
            if (isEmptyParams) {
                return this.$fetch(this.$route.path + '/form').then((form: FormGridType) => {
                    this.richTable.form = form
                })
            } else {
                return this.$processRichTableResponse(this.$fetch(this.$route.path + '/data', params), this.richTable)
            }
        },
        onReload (params: Values) {
            this.$historyReplaceParams(params)
            return this.reload(params)
        },
        onOpenComments(row: FinanceUsersTableRow): void {
            this.openPopup(row, 'commentsPopupOpened');
        },
        onOpenTransactions(row: FinanceUsersTableRow): void {
            this.openPopup(row, 'transactionsPopupOpened');
        },
        openPopup(row: FinanceUsersTableRow, popupKey: 'commentsPopupOpened' | 'transactionsPopupOpened'): void {
            this.siteUser = { siteId: row.site_id, userId: row.user_id };
            this[popupKey] = true;
        },
    },
})
</script>
