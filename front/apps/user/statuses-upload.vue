<template>
    <card>
        <div class="row">
            <div class="col-sm-3">
                <form-grid
                    enctype="formData"
                    v-bind="resp.form"
                    @change="resp.form.values = $event"
                    @submitFormData="onSubmit"
                />
            </div>
            <div class="col-sm-9">
                <p>
                    CSV file with <code>{{ resp.delimiter }}</code> delimiter and <code>{{ resp.columns }}</code> header.
                </p>
                <p>
                    Empty field does not change anything. To clean <code>PM first contact at</code> use <code>(EMPTY)</code> value
                </p>
                <p>
                    <a
                        href="/front/user/statuses-upload/example"
                        target="_blank"
                        download="example.csv"
                    ><icona name="icn-link" /> Example file</a>
                </p>
            </div>
        </div>
        <div class="mb-3 row">
            <div class="col-sm-6">
                <h4>Projects:</h4>
                <table class="table table-bordered table-condensed">
                    <tr>
                        <th>Project Id</th>
                        <th>Name</th>
                    </tr>
                    <tr v-for="site in resp.sites">
                        <td>{{ site.id }}</td>
                        <td>{{ site.name }}</td>
                    </tr>
                </table>
            </div>
            <div class="col-sm-6">
                <h4>Flags:</h4>
                <table
                    v-if="resp.permissions.canChangeUserStatus"
                    class="table table-bordered table-condensed"
                >
                    <tr>
                        <th>Status Id</th>
                        <th>Name</th>
                    </tr>
                    <tr v-for="status in resp.statuses">
                        <td>{{ status.id }}</td>
                        <td>{{ status.name }}</td>
                    </tr>
                </table>
                <hr>
                <table
                    v-if="resp.permissions.canChangeUserStatus"
                    class="table table-bordered table-condensed"
                >
                    <tr>
                        <th>Active Status Id</th>
                        <th>Name</th>
                    </tr>
                    <tr v-for="status in resp.activeStatuses">
                        <td>{{ status.id }}</td>
                        <td>{{ status.name }}</td>
                    </tr>
                </table>
                <hr>
                <table
                    v-if="resp.permissions.canChangeUserStatus"
                    class="table table-bordered table-condensed"
                >
                    <tr>
                        <th>Is Manual</th>
                        <th>Name</th>
                    </tr>
                    <tr>
                        <td>0</td>
                        <td>No (auto)</td>
                    </tr>
                    <tr>
                        <td>1</td>
                        <td>Yes (manual)</td>
                    </tr>
                </table>
                <hr>
                <table
                    v-if="resp.permissions.canChangeCrmGroup"
                    class="table table-bordered table-condensed"
                >
                    <tr>
                        <th>CRM group Id</th>
                        <th>Name</th>
                    </tr>
                    <tr v-for="group in resp.crmGroups">
                        <td>{{ group.id }}</td>
                        <td>{{ group.name }}</td>
                    </tr>
                </table>
                <hr>
                <a
                    v-if="resp.permissions.canChangeUserPm"
                    href="/front/user/statuses-upload/personal-managers"
                    target="_blank"
                    download="personal_managers.csv"
                ><icona name="icn-users" /> Personal managers (csv file)</a>
            </div>
        </div>
    </card>
</template>

<script lang="ts">
import { FormGrid, Icona } from '@/components'
import { Card } from '@/widgets'
import { defineComponent } from 'vue'
import { FormGridType, Item } from '@/types'

type idNameDict = Item[];

interface ServerResponse {
    form: FormGridType
    delimiter: string
    columns: string
    sites: idNameDict
    statuses: idNameDict
    activeStatuses: idNameDict
    crmGroups: idNameDict
    permissions: {
        canChangeUserStatus: boolean
        canChangeUserPm: boolean
        canChangeCrmGroup: boolean
    };
}

// TODO: refactor to use csv-upload component
export default defineComponent({
    components: {
        Icona,
        Card,
        FormGrid,
    },

    data () {
        return {
            resp: {
                richTable: {},
                delimiter: '',
                columns: '',
                sites: [],
                statuses: [],
                activeStatuses: [],
                crmGroups: [],
                permissions: {
                    canChangeUserStatus: false,
                    canChangeUserPm: false,
                    canChangeCrmGroup: false,
                },
            } as ServerResponse,
        }
    },
    async mounted () {
        this.resp = await this.$fetch<ServerResponse>(this.$route.path + '/form')
    },

    methods: {
        onSubmit (formData: FormData) {
            this.$processFormResponse(this.$fetch(this.$route.path + '/upload', formData, false), this.resp.form)
        },
    },
})
</script>
