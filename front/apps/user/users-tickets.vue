<template>
    <Card width="wide">
        <div class="row">
            <div class="col-8">
                <RichTable
                    v-bind="richTable"
                    showPagination
                    showReset
                    showRefresh
                    :reloadOnChange="false"
                    class="table-hover"
                    @change="onChange"
                    @reload="reload"
                >
                    <template #siteUser="{ row } : { row : UserTicketRow}">
                        <a :href="row.player_url" target="_blank"> {{ row.siteUser }} </a>
                    </template>
                    <template #id="{ row } : {row : UserTicketRow }">
                        <button class="btn btn-link" @click="onTicketSelect(row.id)">
                            {{ row.id }}
                            <Icona name="icn-eye" size="lg" />
                        </button>
                    </template>
                </RichTable>
            </div>
            <div class="col-4">
                <template v-if="selectedTicketInfo">
                    <div class="card sticky-top">
                        <div v-if="selectedTicketInfo.allowDecisionButtons" class="card-header btn-group btn-group-sm">
                            <button
                                class="btn btn-xs btn-success"
                                @click="onDecision({ 'id' : selectedTicketId, 'approve' : true })"
                            >
                                <Icona name="icn-thumb-up" /> Approve
                            </button>
                            <button
                                class="btn btn-xs btn-warning"
                                @click="onDecision({ 'id' : selectedTicketId, 'approve' : false })"
                            >
                                <Icona name="icn-thumb-down" /> Decline
                            </button>
                        </div>
                        <div class="card-body">
                            <dl class="row">
                                <template v-for="(field, index) in selectedTicketInfo.details" :key="index">
                                    <dt class="col-4">{{ field.label }}</dt>
                                    <dd class="col-8">
                                        <template v-if="field.routerUrl">
                                            <router-link :to="field.routerUrl">{{ field.value }}</router-link>
                                        </template>
                                        <template v-else-if="field.hrefUrl">
                                            <a :href="field.hrefUrl" target="_blank">{{ field.value }}</a>
                                        </template>
                                        <template v-else>
                                            {{ field.value }}
                                        </template>
                                    </dd>
                                </template>
                            </dl>
                            <div v-if="selectedTicketInfo.files.length" class="row">
                                <h4>Files</h4>
                                <ol class="ms-4">
                                    <template v-for="(file, _index) in selectedTicketInfo.files" :key="_index">
                                        <li>
                                            <template v-if="file.url">
                                                <a :href="file.url" target="_blank">{{ file.original_name }}</a>
                                            </template>
                                            <template v-else>
                                                {{ file.original_name }}
                                            </template>
                                        </li>
                                    </template>
                                </ol>
                            </div>
                            <h4>History</h4>
                            <EntityTable
                                v-bind="selectedTicketInfo.history"
                                :showRowsCount="false"
                            />
                        </div>
                    </div>
                </template>
            </div>
        </div>
    </Card>
</template>

<script lang="ts">
import { Icona, EntityTable, RichTable } from '@/components'
import { Card } from '@/widgets'
import { defineComponent, TdHTMLAttributes } from 'vue'
import { Values, NextWithReload, RichTableType, TableRow, TableType } from '@/types'

interface UserTicketRow extends TableRow {
    id: number
    product_ticket_id: number |null
    jira_key: string | null
    jira_url: string | null
    player_url: string
    siteUser: string
    user_status: string
    class?: string | undefined
}

interface SelectedTicketResponse {
    details: TicketDetailItem[]
    history: TableType
    files: TicketFile[]
    allowDecisionButtons: boolean
}

interface TicketDetailItem {
    label: string
    value: string
    routerUrl?: string
    hrefUrl?: string
}

interface TicketFile {
    original_name: string
    url: null | string
}

export default defineComponent({
    components: {
        Icona,
        Card,
        RichTable,
        EntityTable,
    },
    beforeRouteEnter (to, _from, next: NextWithReload) {
        next(async vm => {
            const queryParams = vm.$decodeParams(to.query)
            await vm.reload(queryParams)
            if (queryParams.ticketId) {
                queryParams.ticketId = parseInt(queryParams.ticketId as string)
            }
            vm.apply(queryParams)
        })
    },
    data: function () {
        return {
            richTable: {} as RichTableType,
            selectedTicketId: undefined as number | undefined,
            selectedTicketInfo: undefined as SelectedTicketResponse | undefined,
        }
    },
    watch: {
        selectedTicketId: {
            async handler (ticketId: number | undefined) {
                if (ticketId) {
                    this.selectedTicketInfo = await this.$fetch(this.$route.path + '/view', {ticketId})
                } else {
                    this.selectedTicketInfo = undefined
                }
            },
        },
    },
    methods: {
        onChange (params: Values) {
            this.richTable.form!.values = params
            this.$historyReplaceParams(Object.assign({}, params))
        },
        async reload (params: Values) {
            this.selectedTicketId = undefined
            return this.$processRichTableResponse(this.$fetch(this.$route.path + '/data', params), this.richTable)
        },
        apply (params: Values) {
            if (params.ticketId) {
                this.onTicketSelect(params.ticketId as number)
            }
        },
        onTicketSelect(ticketId: number) {
            this.selectedTicketId = ticketId
            this.$historyReplaceParams(Object.assign({}, this.richTable.form!.values, {ticketId}))
            this.richTable!.table!.rowProps = ((row: UserTicketRow) => {
                if (this.selectedTicketId === row.id) {
                    return { class: 'table-warning' }
                }
            }) as (row: TableRow) => TdHTMLAttributes
        },
        onDecision(values: Values) {
            this.$processFormResponse(this.$fetch('/user/player/tickets/approve', Object.assign({}, values)), {})
        },
    },
})
</script>

