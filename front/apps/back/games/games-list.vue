<template>
    <div>
        <RichTable
            v-bind="richTable"
            show-pagination
            @reload="onReload"
        >
            <template #platform="{row, refreshCallback}">
                <InplaceEdit
                    :value="row.platform_id"
                    type="select"
                    :list="row.allPlatforms"
                    @submit="onUpdate({id: row.game_id, platform: $event, column: 'platform' }, refreshCallback)"
                />
            </template>
            <template #vendor="{row, refreshCallback}">
                <InplaceEdit
                    :value="row.vendor_id"
                    type="select"
                    :list="row.allVendors"
                    @submit="onUpdate({id: row.game_id, vendor_id: $event, column: 'vendor_id' }, refreshCallback)"
                />
            </template>
            <template #sources="{row}">
                <span
                    v-for="source in row.sources"
                    class="badge bg-secondary m-1"
                >{{ source }}</span>
            </template>

            <template #branded="{row, refreshCallback}: {row: TableRowWithGameAndName, refreshCallback: (() => void)}">
                <button
                    type="button"
                    class="btn btn-xs"
                    :class="row.is_branded ? ' btn-success' : 'btn-secondary'"
                    @click="onUpdate({id: row.game_id, is_branded: !row.is_branded, column: 'is_branded' }, refreshCallback)"
                >
                    <Icona :name="row.is_branded ? 'icn-star' : 'icn-star-off'" />
                    {{ row.is_branded ? ' Branded' : ' Common' }}
                </button>
            </template>

            <template #type="{row, refreshCallback}">
                <div class="btn-group btn-group-sm">
                    <template v-for="t in row.allTypes">
                        <button
                            :class="'btn btn-' + (t.id === row.type ? 'primary active' : 'secondary')"
                            :title="t.name"
                            @click="onUpdate({id: row.game_id, type: t.id, column: 'type' }, refreshCallback)"
                        >
                            {{ t.name[0] }}
                        </button>
                    </template>
                </div>
            </template>
            <template #actions="{row, refreshCallback}">
                <div class="btn-group btn-group-sm">
                    <button
                        type="button"
                        class="btn btn-warning"
                        @click="onSourcesOpen(row, $event.target)"
                    >
                        <Icona name="icn-pencil" /> Edit sources
                    </button>
                    <button
                        v-if="!mergeGame"
                        type="button"
                        class="btn btn-primary"
                        @click="enableMerge(row)"
                    >
                        <Icona name="icn-merge" /> Merge sources
                    </button>
                    <template v-else>
                        <button
                            v-if="mergeGame.game_id === row.game_id"
                            type="button"
                            class="btn btn-danger"
                            @click="disableMerge"
                        >
                            <Icona name="icn-undo" /> Cancel merge
                        </button>

                        <button
                            v-else
                            type="button"
                            class="btn btn-success"
                            @click="mergeInto(row, refreshCallback)"
                        >
                            <Icona name="icn-login" /> Merge into
                        </button>
                    </template>
                </div>
            </template>
        </RichTable>

        <PopoverSingleton
            :popoverTargetClosure="sourceButtonElementClosure"
            title="Dependencies"
            wide="fit-content"
            position="left"
            @close="onSourcesClose"
        >
            <EntityTable v-bind="sourceContent">
                <template #value="{row}">
                    <InplaceEdit
                        :value="row.value"
                        type="textarea"
                        @submit="onUpdateSources({id: row.id, source_id: row.source_id, value: $event})"
                    />
                </template>
            </EntityTable>
        </PopoverSingleton>
    </div>
</template>

<script lang="ts">
import { RichTable, InplaceEdit, EntityTable, Icona, PopoverSingleton } from '@/components'
import { defineComponent } from 'vue'
import { FormGridType, NextWithReload, Values, TableType } from '@/types'

interface TableRowWithGameAndName {
    game_id: number
    is_branded: boolean
    name: string
}

export default defineComponent({
    components: {
        PopoverSingleton,
        Icona,
        RichTable,
        InplaceEdit,
        EntityTable,
    },

    beforeRouteEnter (to, _from, next: NextWithReload) {
        next(vm => vm.reload(vm.$decodeParams(to.query)))
    },

    data () {
        return {
            richTable: {
                form: {} as FormGridType,
            },
            mergeGame: null as TableRowWithGameAndName | null,
            sourcesPopover: {
                table: {} as TableType,
                isOpened: false,
                refreshCallback: undefined as (() => void) | undefined,
            },
            sourceButtonElementClosure: undefined as (() => Element) | undefined,
            sourceContent: null as TableType | null,
            sourceTaskId: null as number | null,
        }
    },

    methods: {
        reload (params: Values) {
            this.$processRichTableResponse(this.$fetch(this.$route.path + '/data', params), this.richTable)
        },
        onReload (params: Values) {
            this.$historyReplaceParams(params)
            return this.reload(params)
        },
        onUpdate (params: Values, refreshCallback: () => void) {
            this.$fetch(this.$route.path + '/update', params).then(refreshCallback)
        },
        onUpdateSources (params: Values) {
            this.$fetch(this.$route.path + '/sources-update', params).then(() => {
                this.sourcesLoad({ id: params.id })
                this.reload({})
            })
        },
        async sourcesLoad (params: Values) {
            return this.$fetch(this.$route.path + '/sources-open', params).then((data: TableType) => {
                this.sourceContent = data
            })
        },
        onSourcesOpen (row: TableRowWithGameAndName, el: EventTarget | null) {
            if (this.sourceButtonElementClosure !== undefined) {
                this.onSourcesClose()
            }

            this.sourcesLoad({id: row.game_id}).then(() => {
                this.sourceButtonElementClosure = () => (el as Element)
            })
        },
        onSourcesClose () {
            this.sourceButtonElementClosure = undefined
            this.sourceTaskId = null
            this.sourceContent = null
        },
        enableMerge (row: TableRowWithGameAndName) {
            this.mergeGame = row
        },
        disableMerge () {
            this.mergeGame = null
        },
        mergeInto (row: TableRowWithGameAndName, refreshCallback: () => void) {
            if (this.mergeGame === null) {
                return
            }

            if (this.mergeGame.game_id === row.game_id) {
                this.mergeGame = null
                return
            }

            if (!confirm('Merge game ' + this.mergeGame.game_id + ' into game ' + row.game_id)) {
                return
            }

            this.$fetch(this.$route.path + '/merge-games', {
                game_from: this.mergeGame.game_id,
                game_to: row.game_id,
            }).then(() => {
                this.mergeGame = null
                refreshCallback()
            })
        },
    },
})
</script>
