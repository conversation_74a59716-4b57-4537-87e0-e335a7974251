<template>
    <card width="wide">
        <richTable
            v-bind="employeeTable"
            showPagination
            showTotal
            showRefresh
            sticky
            @reload="onReload"
        >
            <template #afterTitle>
                <popover
                    title="Create new employee"
                    position="right"
                    @open="onAddEmployeeBtnClick"
                >
                    <button
                        type="button"
                        class="btn btn-sm btn-success"
                    >
                        <icona name="icn-plus-user" /> Create new
                    </button>
                    <template #content>
                        <formGrid
                            v-bind="createForm"
                            @change="createForm.values = $event"
                            @submit="onAddFormSubmit"
                        />
                    </template>
                </popover>

                <button
                    class="btn btn-sm btn-info ms-2"
                    type="button"
                    @click="onDownload"
                >
                    <icona name="icn-download" />
                    CSV
                </button>
            </template>

            <template #email="{row}: {row: EmployeeRow}">
                <routerLink :to="{name: 'employees-update', params: {employeeId: row.employee_id}}">
                    {{ row.email }}
                </routerLink>
            </template>
            <template #status="{row}: {row: EmployeeRow}">
                <icona
                    name="icn-circle-fill"
                    :class="statusDict[row.status]"
                />
            </template>
            <template #private="{row}: {row: EmployeeRow}">
                <icona
                    name="icn-circle-fill"
                    :class="boolClass(row.private)"
                />
            </template>
            <template #payment="{row}: {row: EmployeeRow}">
                <icona
                    name="icn-circle-fill"
                    :class="boolClass(row.payment)"
                />
            </template>
            <template #pgp="{row, refreshCallback}: {row: EmployeeRow, refreshCallback: (() => void)}">
                <icona
                    name="icn-circle-fill"
                    :class="boolClass(row.pgp)"
                />
                <button
                    type="button"
                    class="btn btn-secondary btn-xs ms-1"
                    title="Refresh PGP key"

                    @click="onRefreshPgp(row, refreshCallback)"
                >
                    <icona
                        name="icn-refresh"
                        :class="{'icona-spinner': loadingRow[row.employee_id]}"
                    />
                </button>
            </template>
            <template #roles_only="{row}: {row: EmployeeRow}">
                <icona
                    name="icn-circle-fill"
                    :class="boolClass(row.roles_only)"
                />
            </template>
            <template #actions="{row}">
                <div class="btn-group btn-group-sm">
                    <routerLink
                        class="btn btn-primary"
                        :to="{name: 'employees-update', params: {employeeId: row.employee_id}}"
                    >
                        <icona name="icn-pencil" /> Edit
                    </routerLink>
                    <button
                        type="button"
                        class="btn btn-warning"
                        @click="onLogin(row)"
                    >
                        <icona name="icn-login" /> Login
                    </button>
                </div>
            </template>
        </richTable>
    </card>
</template>

<script lang="ts">
import { RichTable, FormGrid, Popover, Icona } from '@/components'
import { defineComponent } from 'vue'
import { download } from '@/utils'
import { Values, FormGridType, TableRow, NextWithReload, RichTableType } from '@/types'
import { Card } from '@/widgets'
import { useAuthUser } from '@/utils/auth-user.ts'

interface EmployeeRow {
    employee_id: string
    email: string
    status: 0 | 1 | 2
    private: boolean
    payment: boolean
    pgp: boolean
    roles_only: boolean
}

export default defineComponent({
    components: {
        Icona,
        Card,
        RichTable,
        FormGrid,
        Popover,
    },
    beforeRouteEnter (to, _from, next: NextWithReload) {
        next(vm => vm.reload(vm.$decodeParams(to.query)))
    },
    setup() {
        return {
            authUser: useAuthUser(),
        }
    },
    data () {
        return {
            statusDict: {
                0: 'text-danger', // Blocked
                1: 'text-success', // Active
                2: 'text-warning icona-spinner', // Wait
            },
            employeeTable: {} as RichTableType,
            createForm: {} as FormGridType,
            loadingRow: {} as Record<string, boolean>,
        }
    },
    methods: {
        backUrl (postfix: string) {
            return `/back/employees${postfix}`
        },
        async reload (params: Values) {
            await this.$processRichTableResponse(this.$fetch(this.backUrl('/list'), params), this.employeeTable)
        },

        onAddEmployeeBtnClick () {
            this.$fetch(this.backUrl('/create-form')).then((data: FormGridType) => {
                this.createForm = data
            })
        },

        async onAddFormSubmit (params: Values) {
            await this.$processFormResponse(this.$fetch(this.backUrl('/create'), params), this.createForm)
            await this.reload(this.employeeTable.form?.values || {})
        },

        onReload (params: Values) {
            this.$historyReplaceParams(params)
            return this.reload(params)
        },

        onLogin (row: TableRow) {
            this.$fetch(this.backUrl('/login'), { id: row.employee_id }).then(async () => {
                this.authUser.clearStorage()
                await this.authUser.reloadConfig()

                this.$router.push({ path: '/' })
            })
        },

        async onDownload () {
            const params = this.employeeTable.form?.values
            return this.$fetch(this.backUrl('/download'), params).then((csv: string) => {
                download(csv, 'employees.csv')
            })
        },

        boolClass (v: boolean) {
            return v ? 'text-success' : 'text-danger'
        },

        onRefreshPgp (row: EmployeeRow, refreshCallback: () => void) {
            const employeeId = row.employee_id;
            this.loadingRow[employeeId] = true;
            this.$fetch(this.backUrl('/refresh-pgp'), { employeeId })
                .then(() => {
                    this.loadingRow[employeeId] = false;
                })
                .finally(refreshCallback);
        },
    },
})
</script>
