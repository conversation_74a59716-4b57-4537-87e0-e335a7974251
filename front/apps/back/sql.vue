<template>
    <Card width="wide">
        <RichTable
            v-bind="richTable"
            showRefresh
            @reload="onReload"
        >
            <template #query="{row}: {row: SqlTableRow}">
                <div
                    class="small text-start"
                    v-html="row.query"
                />
                <template v-if="row.block_query">
                    <hr>
                    <div
                        class="small text-start"
                        v-html="row.block_query"
                    />
                </template>
            </template>
            <template #actions="{row, refreshCallback}: {row: SqlTableRow, refreshCallback: () => void}">
                <button
                    type="button"
                    class="btn btn-xs btn-danger"
                    @click="onKill(row, refreshCallback)"
                >
                    <Icona name="icn-delete" /> Kill
                </button>
            </template>
        </RichTable>
    </Card>
</template>

<script lang="ts">
import { Icona, RichTable } from '@/components'
import { Card } from '@/widgets'
import { defineComponent } from 'vue'
import { NextWithReload, RichTableType, Values } from '@/types.ts'

interface SqlTableRow {
    id: number
    role: string
    query: string,
    block_query?: string
}

export default defineComponent({
    components: {
        Icona,
        Card,
        RichTable,
    },
    beforeRouteEnter (to, _from, next: NextWithReload) {
        next(vm => vm.reload(vm.$decodeParams(to.query)))
    },
    data: function () {
        return {
            richTable: {} as RichTableType,
        }
    },
    methods: {
        reload (params: Values) {
            this.$processRichTableResponse(this.$fetch('/back/sql/info', params), this.richTable)
        },
        onReload (params: Values) {
            this.$historyReplaceParams(params)
            return this.reload(params)
        },
        onKill (row: SqlTableRow, refreshCallback: () => void) {
            return this.$fetch('/back/sql/kill', {
                id: row.id,
                role: row.role,
            }).then(refreshCallback)
        },
    },
})
</script>
