<template>
    <div>
        <RichTable
            v-bind="richTable"
            :showTitle="false"
            :showTotal="false"
            showTableHeader
            showPagination
            @reload="onReload"
        >
            <template #createComment="{ refreshCallback }">
                <div class="input-group">
                    <input
                        ref="commentInput"
                        v-model="newComment"
                        type="text"
                        class="form-control"
                        placeholder="Enter comment"
                        @keydown.enter="createComment(refreshCallback)"
                    >
                    <button
                        type="button"
                        class="btn btn-success btn-sm"
                        @click="createComment(refreshCallback)"
                    >
                        <Icona name="icn-plus" />
                    </button>
                </div>
            </template>
            <template #comment="{row, refreshCallback}">
                <InplaceEdit
                    v-if="row.is_editable"
                    :key="row.id"
                    :value="row.comment"
                    size="sm"
                    type="textarea"
                    showDelete
                    @submit="updateComment(row.id, $event, refreshCallback)"
                    @delete="deleteComment(row.id, refreshCallback)"
                />
                <span v-else>{{ row.comment }}</span>
            </template>
            <template #updatedAt="{ row }">
                <small class="text-muted">{{ row.updated_at }}</small>
            </template>
            <template #updatedBy="{ row }">
                <small class="text-muted">{{ row.updated_by }}</small>
            </template>
        </RichTable>
    </div>
</template>

<script lang="ts">
import { defineComponent, nextTick, PropType } from 'vue'
import { Icona, InplaceEdit, RichTable } from '@/components'
import { RichTableType, Values } from '@/types'

export default defineComponent({
    name: 'Comments',
    components: {
        InplaceEdit,
        RichTable,
        Icona,
    },
    props: {
        siteUser: {
            type: Object as PropType<{ siteId: number, userId: number }>,
            required: true,
        },
    },
    data() {
        return {
            richTable: {} as RichTableType,
            newComment: '',
        }
    },
    mounted() {
        this.onReload(this.siteUser)
    },
    methods: {
        onReload(params: Values) {
            this.$fetch('/finance/users/get-withdraw-comments', params).then((data: RichTableType) => {
                this.richTable = data
                nextTick(() => {
                    (this.$refs.commentInput as HTMLInputElement).focus();
                });
            })
        },
        appendSiteUserParams (params: Values) {
            return Object.assign({}, params, this.siteUser)
        },
        async createComment(refreshCallback: () => void) {
            this.$fetch('/finance/users/create-withdraw-comment', this.appendSiteUserParams({ comment: this.newComment }))
                .then(() => {
                    refreshCallback()
                    this.newComment = ''
                })
        },
        async updateComment (id: number, comment: string, refreshCallback?: () => void) {
            return this.$fetch('/finance/users/update-withdraw-comment', this.appendSiteUserParams({ comment, id: id }))
                .then(refreshCallback)
        },

        async deleteComment (id: number, refreshCallback?: () => void) {
            return this.$fetch('/finance/users/delete-withdraw-comment', this.appendSiteUserParams({ id: id }))
                .then(refreshCallback)
        },
    },
})
</script>
