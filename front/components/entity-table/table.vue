<template>
    <table
        :class="tableClassesOrDefault"
        v-bind="$attrs"
    >
        <caption
            v-if="showRowsCount"
            class="fst-italic position-absolute"
            style="caption-side: top; font-size: 0.65rem; line-height: 1; top: 0; z-index: 1"
        >
            {{ rows.length }}<br>rows
        </caption>
        <THeader
            v-if="showHeader"
            :columns="columns"
            :sort="sort"
            :disabled="disabled"
            :cellProps="headCellProps"
            :class="{ 'sticky-top': sticky }"
            @sortChanged="sortChanged"
        >
            <template #thead="theadProps">
                <slot
                    name="thead"
                    v-bind="theadProps"
                />
            </template>
        </THeader>

        <tbody>
            <tr v-if="rows.length === 0">
                <td
                    class="text-center"
                    :colspan="columns.length"
                >
                    <em>No data</em>
                </td>
            </tr>
            <RowItem
                v-for="(row, rowIndex) in rows"
                v-bind="rowProps ? rowProps(row) : {}"
                :key="rowKeyName ? (row[rowKeyName] as string) : undefined"
                :row="row"
                :rowIndex="rowIndex"
                :columns="columns"
                :cellProps="rowCellProps"
            >
                <template
                    v-for="slotName in rowsSlotNames"
                    #[slotName]="scope"
                >
                    <slot
                        v-bind="scope"
                        :name="slotName"
                    />
                </template>
            </RowItem>
        </tbody>
    </table>
</template>

<script lang="ts">
import THeader from './header.vue'
import RowItem from './row.vue'
import { TableRow, TableColumn, ScalarOrEmpty } from '@/types'
import { defineComponent, PropType, TdHTMLAttributes } from 'vue'

const intlCollator = new Intl.Collator(undefined, { numeric: true, sensitivity: 'base' })

export function universalSort<R extends TableRow> (lRow: R, rRow: R, dir: -1 | 1, colName: string): number {
    let l = lRow[colName] as ScalarOrEmpty
    let r = rRow[colName] as ScalarOrEmpty

    if (typeof l === 'string' && typeof r === 'string') {
        const lReplaced = l.replace(/[ %]/g, '').replace(/,/g, '.')
        const rReplaced = r.replace(/[ %]/g, '').replace(/,/g, '.')

        if (lReplaced !== '' && !isNaN(Number(lReplaced)) && rReplaced !== '' && !isNaN(Number(rReplaced))) {
            l = Number(lReplaced)
            r = Number(rReplaced)

            return dir * (l - r)
        }
    }

    // sort empty values last
    if (l === '' || l === null || l === undefined) {
        return 1
    }

    if (r === '' || r === null || r === undefined) {
        return -1
    }

    l = String(l)
    r = String(r)

    return dir * intlCollator.compare(l, r)
}

export default defineComponent({
    components: {
        THeader,
        RowItem,
    },

    inheritAttrs: false,

    props: {
        rowKeyName: {
            type: String,
            default: undefined,
        },
        tableClasses: {
            type: Object,
            default: undefined,
        },
        columns: {
            type: Array as PropType<TableColumn[]>,
            default: () => [],
            validator: (columns: TableColumn[]) => {
                for (const { code, sortable } of columns) {
                    if (!code && sortable) {
                        // eslint-disable-next-line no-console
                        console.warn('table.columns: Code must be specified for sortable column')
                        return false
                    }
                }
                return true
            },
        },
        sort: {
            type: String,
            default: '',
        },
        sortCallback: {
            type: Function as PropType<typeof universalSort>,
            default: undefined,
        },
        enableInnerSort: {
            type: Boolean,
            default: false,
        },
        data: {
            type: Array as PropType<TableRow[]>,
            default: () => [],
        },

        /**
         * Callback returns props which will be bind to a specific row.
         * Accepts current row data object as a first argument and columns info as second one.
         */
        rowProps: {
            type: Function as PropType<(row: TableRow) => TdHTMLAttributes>,
            default: () => ({}),
        },

        /**
         * Callback returns props which will be bind to a specific row cell.
         * Accepts column info as a first argument and current row data object as second one.
         */
        rowCellProps: {
            type: Function as PropType<(row: TableRow, column: TableColumn) => TdHTMLAttributes>,
            default: (_row: TableRow, col: TableColumn): TdHTMLAttributes => {
                const props: TdHTMLAttributes = {}
                props.class = {}

                if (col.longTextBrake) {
                    props.class['long-text-brake'] = true
                }

                if (col.nowrap) {
                    props.class['text-nowrap'] = true
                }

                if (col.align) {
                    props.class[`text-${col.align}`] = true
                }

                if (col.style) {
                    props.style = col.style
                }

                return props
            },
        },

        /**
         * Callback returns props which will be bind to a header cell.
         */
        headCellProps: {
            type: Function as PropType<(column: TableColumn) => TdHTMLAttributes>,
            default: (col: TableColumn) => {
                const props: TdHTMLAttributes = {}
                props.class = {}

                if (col.align) {
                    props.class[`text-${col.align}`] = true
                }

                if (col.style) {
                    props.style = col.style
                }

                return props
            },
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        showHeader: {
            type: Boolean,
            default: true,
        },
        showRowsCount: {
            type: Boolean,
            default: true,
        },
        sticky: {
            type: Boolean,
            default: false,
        },
    },

    emits: ['sortChanged'],

    computed: {
        tableClassesOrDefault () {
            return Object.assign({
                table: true,
                'table-striped': true,
                'text-end': true,
                'table-sm': true,
                'table-entity': true,
                'entity-table-disabled': this.disabled,
            }, this.tableClasses || {})
        },
        sortColNameAndDir () : {colName: string; dir: -1 | 1} {
            const colName = this.sort.endsWith('-') ? this.sort.slice(0, -1) : this.sort
            const dir = this.sort.endsWith('-') ? -1 : 1

            return { colName, dir }
        },
        rows (): TableRow[] {
            if ((this.enableInnerSort && this.sort) || this.sortCallback) {
                const { colName, dir } = this.sortColNameAndDir
                const sortCallback = this.sortCallback ? this.sortCallback : universalSort

                return [...this.data].sort((l, r) => sortCallback(l, r, dir, colName));
            }

            return this.data
        },
        rowsSlotNames (): string[] {
            return this.columns
                .map(({slotName}) => slotName)
                .filter(slotName => slotName) as string[]
        },
    },

    methods: {
        sortChanged (newSort: string): void {
            this.$emit('sortChanged', newSort)
        },
    },
})
</script>

<style lang="scss">
    table.table-entity {
        position: relative;
        thead th:first-child {
            // for rows count caption
            padding-left: .7rem;
        }
        td.long-text-brake {
            overflow-wrap: anywhere;
        }
    }
</style>
