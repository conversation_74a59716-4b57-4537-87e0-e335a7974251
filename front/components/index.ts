import { defineAsyncComponent } from 'vue'

// Light or common components
export { default as RichTable } from './rich-table.vue'
export { default as EntityTable } from './entity-table/table.vue'
export { default as FormGrid } from './form/grid.vue'
export { default as FormList } from './form/list.vue'
export { default as FormParams } from './form/params.vue'
export { default as Dropdown } from './dropdown.vue'
export { default as Popover } from './popover.vue'
export { default as PopoverSingleton } from './popover-singleton.vue'
export { default as PopoverRichTable } from './popover-rich-table.vue'
export { default as ProgressBar } from './progress-bar.vue'
export { default as FetchButton } from './fetch-button.vue'
export { default as Tabs } from './tabs.vue'
export { default as TabsRouter } from './tabs-router.vue'
export { default as Tree } from './tree.vue'
export { default as Popup } from './popup.vue'
export { default as InplaceEdit } from './inplace-edit.vue'
export { default as FormError } from './form/error.vue'
export { default as BtnGroup } from './btn-group.vue'
export { default as GalleryImage } from './gallery/gallery-image.vue'
export { default as GalleryImageHeader } from './gallery/gallery-image-header.vue'
export { default as GalleryImageButtons } from './gallery/gallery-image-buttons.vue'
export { default as GalleryImageFooter } from './gallery/gallery-image-footer.vue'
export { default as GalleryImageRotate } from './gallery/gallery-image-rotate.vue'
export { default as PermissionTree } from './permission-tree.vue'
export { default as Icona } from './icona.vue'
export { default as KeyValueList } from './key-value-list.vue'

// Heavy or rare used components
export const HighchartsLoader = () => import('./high-charts.ts')
export const ChatViewer = defineAsyncComponent(() => import('./chat-viewer.vue'))
