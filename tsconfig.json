{
    "compilerOptions": {
        "target": "ESNext",
        "useDefineForClassFields": true,
        "module": "ESNext",
        "lib": [
            "ESNext",
            "DOM",
            "DOM.Iterable"
        ],
        "skipLibCheck": true,

        /* Bundler mode */
        "moduleResolution": "bundler",
        "allowImportingTsExtensions": true,
        "resolveJsonModule": true,
        "isolatedModules": true,
        "noEmit": true,

        /* Linting */
        "strict": true,
        "noUnusedLocals": true,
        "noUnusedParameters": true,
        "noFallthroughCasesInSwitch": true,
        "allowJs": true,
        "strictNullChecks": true,
        "sourceMap": true,

        "baseUrl": ".",
        "paths": {
            "@/*": [
                "front/*"
            ]
        },
        "allowSyntheticDefaultImports": true
        /* for sortablejs */
    },
    "include": [
        "front/**/*.ts",
        "front/**/*.vue"
    ],
    "references": [
        {
            "path": "./tsconfig.node.json"
        }
    ]
}
